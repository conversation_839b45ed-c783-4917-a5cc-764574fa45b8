import 'package:dio/dio.dart';
import 'package:e_library/features/api/dio.dart';
import 'package:e_library/features/api/responses/paginated_response.dart';
import 'package:e_library/features/books/data/models/author.dart';

/// API service for managing authors (admin functionality)
class AuthorsApi {
  /// Get paginated list of authors
  static Future<PaginatedResponse<List<Author>>> getPaginatedAuthors({int page = 1}) async {
    try {
      final response = await dio.get('/authors', queryParameters: {'page': page});
      return PaginatedResponse.fromJsonList(
        response.data as Map<String, dynamic>,
        Author.fromJson,
      );
    } on DioException catch (e) {
      throw AuthorsApiException('Failed to fetch authors: ${e.message}');
    }
  }

  /// Get all authors
  static Future<List<Author>> getAllAuthors() async {
    try {
      final response = await dio.get('/authors');
      final data = response.data as Map<String, dynamic>;
      final authorsData = data['data'] as List<dynamic>;
      return authorsData.map((json) => Author.fromJson(json as Map<String, dynamic>)).toList();
    } on DioException catch (e) {
      throw AuthorsApiException('Failed to fetch authors: ${e.message}');
    }
  }

  /// Get a specific author by ID
  static Future<Author> getAuthor(int id) async {
    try {
      final response = await dio.get('/authors/$id');
      final data = response.data as Map<String, dynamic>;
      return Author.fromJson(data['data'] as Map<String, dynamic>);
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        throw AuthorsApiException('Author not found');
      }
      throw AuthorsApiException('Failed to fetch author: ${e.message}');
    }
  }

  /// Create a new author
  static Future<Author> createAuthor({
    required String firstName,
    required String lastName,
    required String country,
    required String city,
    required String address,
  }) async {
    try {
      final response = await dio.post('/authors', data: {
        'first_name': firstName,
        'last_name': lastName,
        'country': country,
        'city': city,
        'address': address,
      });
      final data = response.data as Map<String, dynamic>;
      return Author.fromJson(data['data'] as Map<String, dynamic>);
    } on DioException catch (e) {
      if (e.response?.statusCode == 422) {
        final errors = e.response?.data['errors'] as Map<String, dynamic>?;
        final errorMessage = _formatValidationErrors(errors);
        throw AuthorsApiException(errorMessage);
      }
      if (e.response?.statusCode == 403) {
        throw AuthorsApiException('Access denied. Admin privileges required.');
      }
      throw AuthorsApiException('Failed to create author: ${e.message}');
    }
  }

  /// Update an existing author
  static Future<Author> updateAuthor(
    int id, {
    required String firstName,
    required String lastName,
    required String country,
    required String city,
    required String address,
  }) async {
    try {
      final response = await dio.put('/authors/$id', data: {
        'first_name': firstName,
        'last_name': lastName,
        'country': country,
        'city': city,
        'address': address,
      });
      final data = response.data as Map<String, dynamic>;
      return Author.fromJson(data['data'] as Map<String, dynamic>);
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        throw AuthorsApiException('Author not found');
      }
      if (e.response?.statusCode == 422) {
        final errors = e.response?.data['errors'] as Map<String, dynamic>?;
        final errorMessage = _formatValidationErrors(errors);
        throw AuthorsApiException(errorMessage);
      }
      if (e.response?.statusCode == 403) {
        throw AuthorsApiException('Access denied. Admin privileges required.');
      }
      throw AuthorsApiException('Failed to update author: ${e.message}');
    }
  }

  /// Delete an author
  static Future<void> deleteAuthor(int id) async {
    try {
      await dio.delete('/authors/$id');
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        throw AuthorsApiException('Author not found');
      }
      if (e.response?.statusCode == 403) {
        throw AuthorsApiException('Access denied. Admin privileges required.');
      }
      throw AuthorsApiException('Failed to delete author: ${e.message}');
    }
  }

  static String _formatValidationErrors(Map<String, dynamic>? errors) {
    if (errors == null) return 'Validation failed';

    final errorMessages = <String>[];
    errors.forEach((field, messages) {
      if (messages is List) {
        errorMessages.addAll(messages.cast<String>());
      }
    });

    return errorMessages.isNotEmpty
        ? errorMessages.join('\n')
        : 'Validation failed';
  }
}

class AuthorsApiException implements Exception {
  final String message;

  const AuthorsApiException(this.message);

  @override
  String toString() => message;
}
