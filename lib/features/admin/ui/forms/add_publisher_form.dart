import 'package:flutter/material.dart';
import 'package:e_library/features/admin/data/services/admin_service.dart';
import 'package:e_library/features/books/data/models/publisher.dart';

class AddPublisherForm extends StatefulWidget {
  final Publisher? publisher; // For editing existing publisher
  final VoidCallback? onSuccess;

  const AddPublisherForm({
    super.key,
    this.publisher,
    this.onSuccess,
  });

  @override
  State<AddPublisherForm> createState() => _AddPublisherFormState();
}

class _AddPublisherFormState extends State<AddPublisherForm> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _cityController = TextEditingController();
  
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.publisher != null) {
      _nameController.text = widget.publisher!.name;
      _cityController.text = widget.publisher!.city ?? '';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _cityController.dispose();
    super.dispose();
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.publisher != null) {
        // Update existing publisher
        await AdminService.updatePublisher(
          widget.publisher!.id,
          name: _nameController.text.trim(),
          city: _cityController.text.trim(),
        );
      } else {
        // Create new publisher
        await AdminService.createPublisher(
          name: _nameController.text.trim(),
          city: _cityController.text.trim(),
        );
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.publisher != null 
                ? 'Publisher updated successfully!' 
                : 'Publisher created successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        
        widget.onSuccess?.call();
        Navigator.of(context).pop();
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $error'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.publisher != null ? 'Edit Publisher' : 'Add Publisher'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'Publisher Name',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter publisher name';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _cityController,
                        decoration: const InputDecoration(
                          labelText: 'City',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter city';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _submitForm,
                  child: _isLoading
                      ? const CircularProgressIndicator()
                      : Text(widget.publisher != null ? 'Update Publisher' : 'Create Publisher'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
