// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'role.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Role _$RoleFromJson(Map<String, dynamic> json) => Role(
  id: (json['id'] as num).toInt(),
  name: json['name'] as String,
  guardName: json['guard_name'] as String,
  createdAt: json['created_at'] as String,
  updatedAt: json['updated_at'] as String,
  pivot: json['pivot'] == null
      ? null
      : RolePivot.fromJson(json['pivot'] as Map<String, dynamic>),
);

Map<String, dynamic> _$RoleToJson(Role instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'guard_name': instance.guardName,
  'created_at': instance.createdAt,
  'updated_at': instance.updatedAt,
  'pivot': instance.pivot,
};

RolePivot _$RolePivotFromJson(Map<String, dynamic> json) => RolePivot(
  modelType: json['model_type'] as String,
  modelId: (json['model_id'] as num).toInt(),
  roleId: (json['role_id'] as num).toInt(),
);

Map<String, dynamic> _$RolePivotToJson(RolePivot instance) => <String, dynamic>{
  'model_type': instance.modelType,
  'model_id': instance.modelId,
  'role_id': instance.roleId,
};
