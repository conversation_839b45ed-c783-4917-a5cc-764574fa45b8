import 'package:json_annotation/json_annotation.dart';
import 'role.dart';

part 'user.g.dart';

@JsonSerializable()
class User {
  final int id;
  final String name;
  final String email;
  @Json<PERSON>ey(name: 'email_verified_at')
  final String? emailVerifiedAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final String createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final String updatedAt;
  @Json<PERSON>ey(name: 'is_admin')
  final bool isAdmin;
  final List<Role>? roles;

  const User({
    required this.id,
    required this.name,
    required this.email,
    this.emailVerifiedAt,
    required this.createdAt,
    required this.updatedAt,
    required this.isAdmin,
    this.roles,
  });

  /// Creates a User from JSON data
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  /// Converts this User to JSON
  Map<String, dynamic> toJson() => _$UserToJson(this);

  /// Check if user has admin privileges
  bool get hasAdminAccess => isAdmin;

  /// Check if user has a specific role
  bool hasRole(String roleName) {
    if (roles == null) return false;
    return roles!.any((role) => role.name == roleName);
  }

  /// Get all role names
  List<String> get roleNames {
    if (roles == null) return [];
    return roles!.map((role) => role.name).toList();
  }
}
