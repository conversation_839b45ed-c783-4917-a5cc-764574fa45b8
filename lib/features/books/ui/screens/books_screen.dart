import 'package:e_library/features/admin/ui/forms/add_book_form.dart';
import 'package:e_library/features/admin/ui/screens/admin_dashboard.dart';
import 'package:e_library/features/auth/data/api.dart';
import 'package:e_library/features/auth/data/auth_service.dart';
import 'package:e_library/features/auth/data/user_service.dart';
import 'package:e_library/features/books/ui/components/books_list.dart';
import 'package:flutter/material.dart';

class BooksScreen extends StatefulWidget {
  const BooksScreen({super.key, required this.title});

  final String title;

  @override
  State<BooksScreen> createState() => _BooksScreenState();
}

class _BooksScreenState extends State<BooksScreen> {
  bool _isAdmin = false;

  @override
  void initState() {
    super.initState();
    _checkAdminStatus();
  }

  Future<void> _checkAdminStatus() async {
    final isAdmin = await UserService.isCurrentUserAdmin(true);
    if (mounted) {
      setState(() {
        _isAdmin = isAdmin;
      });
    }
  }

  Future<void> _handleLogout() async {
    try {
      try {
        await AuthApi.logout();
      } finally {
        await AuthService.removeToken();
      }

      if (mounted) {
        Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Logout failed: $error'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: Text(widget.title),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              Navigator.of(context).pushNamed('/book-search');
            },
            tooltip: 'Search Books',
          ),
          if (_isAdmin)
            IconButton(
              icon: const Icon(Icons.admin_panel_settings),
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const AdminDashboard(),
                  ),
                );
              },
              tooltip: 'Admin Dashboard',
            ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _handleLogout,
            tooltip: 'Logout',
          ),
        ],
      ),
      body: const Center(child: BooksList()),
      floatingActionButton: _isAdmin
          ? FloatingActionButton(
              tooltip: 'Add Book',
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(builder: (context) => const AddBookForm()),
                );
              },
              child: const Icon(Icons.add),
            )
          : null,
    );
  }
}
