import 'package:dio/dio.dart';
import 'package:e_library/features/api/dio.dart';
import 'package:e_library/features/api/responses/paginated_response.dart';
import 'package:e_library/features/books/data/models/publisher.dart';

/// API service for managing publishers (admin functionality)
class PublishersApi {
  /// Get paginated list of publishers
  static Future<PaginatedResponse<List<Publisher>>> getPaginatedPublishers({int page = 1}) async {
    try {
      final response = await dio.get('/publishers', queryParameters: {'page': page});
      return PaginatedResponse.fromJsonList(
        response.data as Map<String, dynamic>,
        Publisher.fromJson,
      );
    } on DioException catch (e) {
      throw PublishersApiException('Failed to fetch publishers: ${e.message}');
    }
  }

  /// Get all publishers
  static Future<List<Publisher>> getAllPublishers() async {
    try {
      final response = await dio.get('/publishers');
      final data = response.data as Map<String, dynamic>;
      final publishersData = data['data'] as List<dynamic>;
      return publishersData.map((json) => Publisher.fromJson(json as Map<String, dynamic>)).toList();
    } on DioException catch (e) {
      throw PublishersApiException('Failed to fetch publishers: ${e.message}');
    }
  }

  /// Get a specific publisher by ID
  static Future<Publisher> getPublisher(int id) async {
    try {
      final response = await dio.get('/publishers/$id');
      final data = response.data as Map<String, dynamic>;
      return Publisher.fromJson(data['data'] as Map<String, dynamic>);
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        throw PublishersApiException('Publisher not found');
      }
      throw PublishersApiException('Failed to fetch publisher: ${e.message}');
    }
  }

  /// Create a new publisher
  static Future<Publisher> createPublisher({
    required String name,
    required String city,
  }) async {
    try {
      final response = await dio.post('/publishers', data: {
        'name': name,
        'city': city,
      });
      final data = response.data as Map<String, dynamic>;
      return Publisher.fromJson(data['data'] as Map<String, dynamic>);
    } on DioException catch (e) {
      if (e.response?.statusCode == 422) {
        final errors = e.response?.data['errors'] as Map<String, dynamic>?;
        final errorMessage = _formatValidationErrors(errors);
        throw PublishersApiException(errorMessage);
      }
      if (e.response?.statusCode == 403) {
        throw PublishersApiException('Access denied. Admin privileges required.');
      }
      throw PublishersApiException('Failed to create publisher: ${e.message}');
    }
  }

  /// Update an existing publisher
  static Future<Publisher> updatePublisher(
    int id, {
    required String name,
    required String city,
  }) async {
    try {
      final response = await dio.put('/publishers/$id', data: {
        'name': name,
        'city': city,
      });
      final data = response.data as Map<String, dynamic>;
      return Publisher.fromJson(data['data'] as Map<String, dynamic>);
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        throw PublishersApiException('Publisher not found');
      }
      if (e.response?.statusCode == 422) {
        final errors = e.response?.data['errors'] as Map<String, dynamic>?;
        final errorMessage = _formatValidationErrors(errors);
        throw PublishersApiException(errorMessage);
      }
      if (e.response?.statusCode == 403) {
        throw PublishersApiException('Access denied. Admin privileges required.');
      }
      throw PublishersApiException('Failed to update publisher: ${e.message}');
    }
  }

  /// Delete a publisher
  static Future<void> deletePublisher(int id) async {
    try {
      await dio.delete('/publishers/$id');
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        throw PublishersApiException('Publisher not found');
      }
      if (e.response?.statusCode == 403) {
        throw PublishersApiException('Access denied. Admin privileges required.');
      }
      throw PublishersApiException('Failed to delete publisher: ${e.message}');
    }
  }

  static String _formatValidationErrors(Map<String, dynamic>? errors) {
    if (errors == null) return 'Validation failed';

    final errorMessages = <String>[];
    errors.forEach((field, messages) {
      if (messages is List) {
        errorMessages.addAll(messages.cast<String>());
      }
    });

    return errorMessages.isNotEmpty
        ? errorMessages.join('\n')
        : 'Validation failed';
  }
}

class PublishersApiException implements Exception {
  final String message;

  const PublishersApiException(this.message);

  @override
  String toString() => message;
}
