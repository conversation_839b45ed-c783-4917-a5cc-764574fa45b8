import 'package:e_library/features/admin/data/api/authors_api.dart';
import 'package:e_library/features/admin/data/api/publishers_api.dart';
import 'package:e_library/features/books/data/api.dart';
import 'package:e_library/features/books/data/models/author.dart';
import 'package:e_library/features/books/data/models/publisher.dart';
import 'package:e_library/features/books/data/models/book.dart';
import 'package:e_library/features/auth/data/user_service.dart';

/// Service that provides admin functionality for content management
class AdminService {
  
  // AUTHORIZATION CHECKS
  
  /// Check if current user has admin access
  static Future<bool> hasAdminAccess() async {
    return await UserService.isCurrentUserAdmin();
  }

  /// Ensure current user has admin access, throw exception if not
  static Future<void> requireAdminAccess() async {
    final hasAccess = await hasAdminAccess();
    if (!hasAccess) {
      throw AdminException('Access denied. Admin privileges required.');
    }
  }

  // AUTHORS MANAGEMENT
  
  /// Get all authors (for dropdowns/selection)
  static Future<List<Author>> getAllAuthors() async {
    return await AuthorsApi.getAllAuthors();
  }

  /// Create a new author (admin only)
  static Future<Author> createAuthor({
    required String firstName,
    required String lastName,
    required String country,
    required String city,
    required String address,
  }) async {
    await requireAdminAccess();
    return await AuthorsApi.createAuthor(
      firstName: firstName,
      lastName: lastName,
      country: country,
      city: city,
      address: address,
    );
  }

  /// Update an existing author (admin only)
  static Future<Author> updateAuthor(
    int id, {
    required String firstName,
    required String lastName,
    required String country,
    required String city,
    required String address,
  }) async {
    await requireAdminAccess();
    return await AuthorsApi.updateAuthor(
      id,
      firstName: firstName,
      lastName: lastName,
      country: country,
      city: city,
      address: address,
    );
  }

  /// Delete an author (admin only)
  static Future<void> deleteAuthor(int id) async {
    await requireAdminAccess();
    return await AuthorsApi.deleteAuthor(id);
  }

  // PUBLISHERS MANAGEMENT
  
  /// Get all publishers (for dropdowns/selection)
  static Future<List<Publisher>> getAllPublishers() async {
    return await PublishersApi.getAllPublishers();
  }

  /// Create a new publisher (admin only)
  static Future<Publisher> createPublisher({
    required String name,
    required String city,
  }) async {
    await requireAdminAccess();
    return await PublishersApi.createPublisher(
      name: name,
      city: city,
    );
  }

  /// Update an existing publisher (admin only)
  static Future<Publisher> updatePublisher(
    int id, {
    required String name,
    required String city,
  }) async {
    await requireAdminAccess();
    return await PublishersApi.updatePublisher(
      id,
      name: name,
      city: city,
    );
  }

  /// Delete a publisher (admin only)
  static Future<void> deletePublisher(int id) async {
    await requireAdminAccess();
    return await PublishersApi.deletePublisher(id);
  }

  // BOOKS MANAGEMENT
  
  /// Create a new book (admin only)
  static Future<Book> createBook({
    required String title,
    required String category,
    required double price,
    required int publisherId,
    required int authorId,
  }) async {
    await requireAdminAccess();
    return await BooksApi.createBook(
      title: title,
      category: category,
      price: price,
      publisherId: publisherId,
      authorId: authorId,
    );
  }

  /// Update an existing book (admin only)
  static Future<Book> updateBook(
    int id, {
    required String title,
    required String category,
    required double price,
    required int publisherId,
    required int authorId,
  }) async {
    await requireAdminAccess();
    return await BooksApi.updateBook(
      id,
      title: title,
      category: category,
      price: price,
      publisherId: publisherId,
      authorId: authorId,
    );
  }

  /// Delete a book (admin only)
  static Future<void> deleteBook(int id) async {
    await requireAdminAccess();
    return await BooksApi.deleteBook(id);
  }
}

class AdminException implements Exception {
  final String message;

  const AdminException(this.message);

  @override
  String toString() => message;
}
