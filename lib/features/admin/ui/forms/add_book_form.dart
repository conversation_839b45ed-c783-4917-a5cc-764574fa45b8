import 'package:flutter/material.dart';
import 'package:e_library/features/admin/data/services/admin_service.dart';
import 'package:e_library/features/books/data/models/book.dart';
import 'package:e_library/features/books/data/models/author.dart';
import 'package:e_library/features/books/data/models/publisher.dart';

class AddBookForm extends StatefulWidget {
  final Book? book; // For editing existing book
  final VoidCallback? onSuccess;

  const AddBookForm({
    super.key,
    this.book,
    this.onSuccess,
  });

  @override
  State<AddBookForm> createState() => _AddBookFormState();
}

class _AddBookFormState extends State<AddBookForm> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _categoryController = TextEditingController();
  final _priceController = TextEditingController();
  
  List<Author> _authors = [];
  List<Publisher> _publishers = [];
  Author? _selectedAuthor;
  Publisher? _selectedPublisher;
  bool _isLoading = false;
  bool _isLoadingData = true;

  @override
  void initState() {
    super.initState();
    _loadData();
    if (widget.book != null) {
      _titleController.text = widget.book!.title;
      _categoryController.text = widget.book!.category;
      _priceController.text = widget.book!.price.toString();
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _categoryController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      final authors = await AdminService.getAllAuthors();
      final publishers = await AdminService.getAllPublishers();
      
      setState(() {
        _authors = authors;
        _publishers = publishers;
        _isLoadingData = false;
        
        // Set selected values if editing
        if (widget.book != null) {
          _selectedAuthor = _authors.firstWhere(
            (author) => author.id == widget.book!.authorId,
            orElse: () => _authors.first,
          );
          _selectedPublisher = _publishers.firstWhere(
            (publisher) => publisher.id == widget.book!.publisherId,
            orElse: () => _publishers.first,
          );
        }
      });
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading data: $error'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
        setState(() {
          _isLoadingData = false;
        });
      }
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;
    
    if (_selectedAuthor == null || _selectedPublisher == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select both author and publisher'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final price = double.parse(_priceController.text.trim());
      
      if (widget.book != null) {
        // Update existing book
        await AdminService.updateBook(
          widget.book!.id,
          title: _titleController.text.trim(),
          category: _categoryController.text.trim(),
          price: price,
          publisherId: _selectedPublisher!.id,
          authorId: _selectedAuthor!.id,
        );
      } else {
        // Create new book
        await AdminService.createBook(
          title: _titleController.text.trim(),
          category: _categoryController.text.trim(),
          price: price,
          publisherId: _selectedPublisher!.id,
          authorId: _selectedAuthor!.id,
        );
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.book != null 
                ? 'Book updated successfully!' 
                : 'Book created successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        
        widget.onSuccess?.call();
        Navigator.of(context).pop();
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $error'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoadingData) {
      return Scaffold(
        appBar: AppBar(
          title: Text(widget.book != null ? 'Edit Book' : 'Add Book'),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.book != null ? 'Edit Book' : 'Add Book'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      TextFormField(
                        controller: _titleController,
                        decoration: const InputDecoration(
                          labelText: 'Book Title',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter book title';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _categoryController,
                        decoration: const InputDecoration(
                          labelText: 'Category',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter category';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _priceController,
                        decoration: const InputDecoration(
                          labelText: 'Price',
                          border: OutlineInputBorder(),
                          prefixText: '\$',
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter price';
                          }
                          if (double.tryParse(value.trim()) == null) {
                            return 'Please enter a valid price';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      DropdownButtonFormField<Author>(
                        value: _selectedAuthor,
                        decoration: const InputDecoration(
                          labelText: 'Author',
                          border: OutlineInputBorder(),
                        ),
                        items: _authors.map((author) {
                          return DropdownMenuItem<Author>(
                            value: author,
                            child: Text(author.fullName),
                          );
                        }).toList(),
                        onChanged: (Author? value) {
                          setState(() {
                            _selectedAuthor = value;
                          });
                        },
                        validator: (value) {
                          if (value == null) {
                            return 'Please select an author';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      DropdownButtonFormField<Publisher>(
                        value: _selectedPublisher,
                        decoration: const InputDecoration(
                          labelText: 'Publisher',
                          border: OutlineInputBorder(),
                        ),
                        items: _publishers.map((publisher) {
                          return DropdownMenuItem<Publisher>(
                            value: publisher,
                            child: Text(publisher.name),
                          );
                        }).toList(),
                        onChanged: (Publisher? value) {
                          setState(() {
                            _selectedPublisher = value;
                          });
                        },
                        validator: (value) {
                          if (value == null) {
                            return 'Please select a publisher';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _submitForm,
                  child: _isLoading
                      ? const CircularProgressIndicator()
                      : Text(widget.book != null ? 'Update Book' : 'Create Book'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
