import 'package:json_annotation/json_annotation.dart';

part 'role.g.dart';

@JsonSerializable()
class Role {
  final int id;
  final String name;
  @Json<PERSON>ey(name: 'guard_name')
  final String guardName;
  @<PERSON><PERSON><PERSON>ey(name: 'created_at')
  final String createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final String updatedAt;
  final RolePivot? pivot;

  const Role({
    required this.id,
    required this.name,
    required this.guardName,
    required this.createdAt,
    required this.updatedAt,
    this.pivot,
  });

  /// Creates a Role from JSON data
  factory Role.fromJson(Map<String, dynamic> json) => _$RoleFromJson(json);

  /// Converts this Role to JSON
  Map<String, dynamic> toJson() => _$RoleToJson(this);
}

@JsonSerializable()
class RolePivot {
  @JsonKey(name: 'model_type')
  final String modelType;
  @JsonKey(name: 'model_id')
  final int modelId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'role_id')
  final int roleId;

  const RolePivot({
    required this.modelType,
    required this.modelId,
    required this.roleId,
  });

  /// Creates a RolePivot from JSON data
  factory RolePivot.fromJson(Map<String, dynamic> json) => _$RolePivotFromJson(json);

  /// Converts this RolePivot to JSON
  Map<String, dynamic> toJson() => _$RolePivotToJson(this);
}
