import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:e_library/features/auth/data/api.dart';
import 'package:e_library/features/auth/data/models/user.dart';

class UserService {
  static const String _currentUserKey = 'current_user';
  static User? _cachedUser;

  /// Get current user from cache or API
  static Future<User?> getCurrentUser({bool forceRefresh = false}) async {
    if (_cachedUser != null && !forceRefresh) {
      return _cachedUser;
    }

    try {
      // Try to get from cache first
      if (!forceRefresh) {
        final cachedUser = await _getCachedUser();
        if (cachedUser != null) {
          _cachedUser = cachedUser;
          return cachedUser;
        }
      }

      // Fetch from API
      final user = await AuthApi.getCurrentUser();
      await _cacheUser(user);
      _cachedUser = user;
      return user;
    } catch (e) {
      // If API call fails, try to return cached user
      if (!forceRefresh) {
        final cachedUser = await _getCachedUser();
        if (cachedUser != null) {
          _cachedUser = cachedUser;
          return cachedUser;
        }
      }
      return null;
    }
  }

  /// Check if current user is admin
  static Future<bool> isCurrentUserAdmin() async {
    final user = await getCurrentUser();
    return user?.hasAdminAccess ?? false;
  }

  /// Check if current user has a specific role
  static Future<bool> currentUserHasRole(String roleName) async {
    final user = await getCurrentUser();
    return user?.hasRole(roleName) ?? false;
  }

  /// Get current user's role names
  static Future<List<String>> getCurrentUserRoles() async {
    final user = await getCurrentUser();
    return user?.roleNames ?? [];
  }

  /// Cache user data locally
  static Future<void> _cacheUser(User user) async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = jsonEncode(user.toJson());
    await prefs.setString(_currentUserKey, userJson);
  }

  /// Get cached user data
  static Future<User?> _getCachedUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_currentUserKey);
      if (userJson != null) {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        return User.fromJson(userMap);
      }
    } catch (e) {
      // If there's an error parsing cached data, clear it
      await clearUserCache();
    }
    return null;
  }

  /// Clear cached user data
  static Future<void> clearUserCache() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_currentUserKey);
    _cachedUser = null;
  }

  /// Refresh user data from API
  static Future<User?> refreshCurrentUser() async {
    return await getCurrentUser(forceRefresh: true);
  }

  /// Initialize user data on app start
  static Future<void> initializeUser() async {
    try {
      await getCurrentUser();
    } catch (e) {
      // Ignore errors during initialization
    }
  }
}
